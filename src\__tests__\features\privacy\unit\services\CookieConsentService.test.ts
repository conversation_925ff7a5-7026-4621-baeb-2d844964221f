// src/__tests__/features/privacy/unit/services/CookieConsentService.test.ts
// Tests unitarios para CookieConsentService

import { CookieConsentService } from '@/features/privacy/services/CookieConsentService';
import { CookieStorageRepository } from '@/features/privacy/services/CookieStorageRepository';
import { CookieConsent, CookiePreferences } from '@/features/privacy/types/cookie.types';
import { COOKIE_CONSENT_VERSION, DEFAULT_COOKIE_PREFERENCES } from '@/features/privacy/constants/cookie.constants';

// Mock del repository
class MockCookieStorageRepository implements CookieStorageRepository {
  private consent: CookieConsent | null = null;
  private preferences: CookiePreferences = DEFAULT_COOKIE_PREFERENCES;

  async getConsent(): Promise<CookieConsent | null> {
    return this.consent;
  }

  async setConsent(consent: CookieConsent): Promise<void> {
    this.consent = consent;
  }

  async clearConsent(): Promise<void> {
    this.consent = null;
    this.preferences = DEFAULT_COOKIE_PREFERENCES;
  }

  async getPreferences(): Promise<CookiePreferences> {
    return this.preferences;
  }

  async setPreferences(preferences: CookiePreferences): Promise<void> {
    this.preferences = preferences;
  }
}

describe('CookieConsentService', () => {
  let service: CookieConsentService;
  let mockRepository: MockCookieStorageRepository;

  beforeEach(() => {
    mockRepository = new MockCookieStorageRepository();
    service = new CookieConsentService(mockRepository);
  });

  describe('getConsentState', () => {
    it('should return default state when no consent exists', async () => {
      const state = await service.getConsentState();
      
      expect(state.hasConsent).toBe(false);
      expect(state.preferences).toEqual(DEFAULT_COOKIE_PREFERENCES);
      expect(state.lastUpdated).toBeNull();
    });

    it('should return consent state when consent exists', async () => {
      const testDate = new Date();
      await mockRepository.setConsent({
        granted: true,
        timestamp: testDate,
        version: COOKIE_CONSENT_VERSION,
      });

      const state = await service.getConsentState();
      
      expect(state.hasConsent).toBe(true);
      expect(state.lastUpdated).toEqual(testDate);
    });
  });

  describe('grantConsent', () => {
    it('should grant consent with default preferences', async () => {
      await service.grantConsent();
      
      const consent = await mockRepository.getConsent();
      const preferences = await mockRepository.getPreferences();
      
      expect(consent?.granted).toBe(true);
      expect(consent?.version).toBe(COOKIE_CONSENT_VERSION);
      expect(preferences).toEqual(DEFAULT_COOKIE_PREFERENCES);
    });

    it('should grant consent with custom preferences', async () => {
      const customPreferences: CookiePreferences = {
        functional: true,
        analytics: true,
        marketing: false,
      };

      await service.grantConsent(customPreferences);
      
      const preferences = await mockRepository.getPreferences();
      expect(preferences).toEqual(customPreferences);
    });
  });

  describe('revokeConsent', () => {
    it('should clear consent and preferences', async () => {
      await service.grantConsent();
      await service.revokeConsent();
      
      const consent = await mockRepository.getConsent();
      expect(consent).toBeNull();
    });
  });

  describe('hasValidConsent', () => {
    it('should return false when no consent exists', async () => {
      const isValid = await service.hasValidConsent();
      expect(isValid).toBe(false);
    });

    it('should return true when valid consent exists', async () => {
      await service.grantConsent();
      const isValid = await service.hasValidConsent();
      expect(isValid).toBe(true);
    });

    it('should return false when consent is not granted', async () => {
      await mockRepository.setConsent({
        granted: false,
        timestamp: new Date(),
        version: COOKIE_CONSENT_VERSION,
      });

      const isValid = await service.hasValidConsent();
      expect(isValid).toBe(false);
    });
  });

  describe('shouldShowBanner', () => {
    it('should return true when no valid consent exists', async () => {
      const shouldShow = await service.shouldShowBanner();
      expect(shouldShow).toBe(true);
    });

    it('should return false when valid consent exists', async () => {
      await service.grantConsent();
      const shouldShow = await service.shouldShowBanner();
      expect(shouldShow).toBe(false);
    });
  });
});
