// src/__tests__/features/privacy/unit/components/CookieBanner.test.tsx
// Tests unitarios para CookieBanner

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CookieBanner from '@/components/ui/CookieBanner';

// Mock del hook useCookieConsent
jest.mock('@/features/privacy/hooks/useCookieConsent', () => ({
  useCookieConsent: jest.fn(),
}));

import { useCookieConsent } from '@/features/privacy/hooks/useCookieConsent';

const mockUseCookieConsent = useCookieConsent as jest.MockedFunction<typeof useCookieConsent>;

describe('CookieBanner', () => {
  const mockGrantConsent = jest.fn();
  const mockRevokeConsent = jest.fn();
  const mockHideBanner = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should not render when showBanner is false', () => {
    mockUseCookieConsent.mockReturnValue({
      consentState: { hasConsent: true, preferences: { functional: true }, lastUpdated: new Date() },
      isLoading: false,
      showBanner: false,
      grantConsent: mockGrantConsent,
      revokeConsent: mockRevokeConsent,
      updatePreferences: jest.fn(),
      hideBanner: mockHideBanner,
    });

    render(<CookieBanner />);
    
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('should not render when isLoading is true', () => {
    mockUseCookieConsent.mockReturnValue({
      consentState: { hasConsent: false, preferences: { functional: true }, lastUpdated: null },
      isLoading: true,
      showBanner: true,
      grantConsent: mockGrantConsent,
      revokeConsent: mockRevokeConsent,
      updatePreferences: jest.fn(),
      hideBanner: mockHideBanner,
    });

    render(<CookieBanner />);
    
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('should render banner when showBanner is true and not loading', () => {
    mockUseCookieConsent.mockReturnValue({
      consentState: { hasConsent: false, preferences: { functional: true }, lastUpdated: null },
      isLoading: false,
      showBanner: true,
      grantConsent: mockGrantConsent,
      revokeConsent: mockRevokeConsent,
      updatePreferences: jest.fn(),
      hideBanner: mockHideBanner,
    });

    render(<CookieBanner />);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Este sitio web utiliza cookies')).toBeInTheDocument();
    expect(screen.getByText('Entendido')).toBeInTheDocument();
  });

  it('should have proper accessibility attributes', () => {
    mockUseCookieConsent.mockReturnValue({
      consentState: { hasConsent: false, preferences: { functional: true }, lastUpdated: null },
      isLoading: false,
      showBanner: true,
      grantConsent: mockGrantConsent,
      revokeConsent: mockRevokeConsent,
      updatePreferences: jest.fn(),
      hideBanner: mockHideBanner,
    });

    render(<CookieBanner />);
    
    const dialog = screen.getByRole('dialog');
    expect(dialog).toHaveAttribute('aria-labelledby', 'cookie-consent-title');
    expect(dialog).toHaveAttribute('aria-describedby', 'cookie-consent-description');
    
    const button = screen.getByRole('button', { name: /aceptar y cerrar banner de cookies/i });
    expect(button).toBeInTheDocument();
  });

  it('should call grantConsent when Entendido button is clicked', async () => {
    mockUseCookieConsent.mockReturnValue({
      consentState: { hasConsent: false, preferences: { functional: true }, lastUpdated: null },
      isLoading: false,
      showBanner: true,
      grantConsent: mockGrantConsent,
      revokeConsent: mockRevokeConsent,
      updatePreferences: jest.fn(),
      hideBanner: mockHideBanner,
    });

    render(<CookieBanner />);
    
    const button = screen.getByText('Entendido');
    fireEvent.click(button);
    
    await waitFor(() => {
      expect(mockGrantConsent).toHaveBeenCalledTimes(1);
    });
  });

  it('should contain link to cookie policy', () => {
    mockUseCookieConsent.mockReturnValue({
      consentState: { hasConsent: false, preferences: { functional: true }, lastUpdated: null },
      isLoading: false,
      showBanner: true,
      grantConsent: mockGrantConsent,
      revokeConsent: mockRevokeConsent,
      updatePreferences: jest.fn(),
      hideBanner: mockHideBanner,
    });

    render(<CookieBanner />);
    
    const link = screen.getByRole('link', { name: /política de cookies/i });
    expect(link).toHaveAttribute('href', '/politica-de-cookies');
  });
});
