// src/__tests__/features/privacy/integration/cookie-consent-flow.test.ts
// Tests de integración para el flujo completo de consentimiento de cookies

import { CookieConsentService } from '@/features/privacy/services/CookieConsentService';
import { LocalStorageCookieRepository } from '@/features/privacy/services/CookieStorageRepository';
import { COOKIE_CONSENT_KEY, COOKIE_PREFERENCES_KEY, COOKIE_CONSENT_VERSION } from '@/features/privacy/constants/cookie.constants';

// Mock de localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value.toString();
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
  };
})();

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('Cookie Consent Flow Integration', () => {
  let repository: LocalStorageCookieRepository;
  let service: CookieConsentService;

  beforeEach(() => {
    localStorageMock.clear();
    repository = new LocalStorageCookieRepository();
    service = new CookieConsentService(repository);
  });

  describe('Complete consent flow', () => {
    it('should handle complete consent granting flow', async () => {
      // Initial state - no consent
      expect(await service.shouldShowBanner()).toBe(true);
      expect(await service.hasValidConsent()).toBe(false);

      const initialState = await service.getConsentState();
      expect(initialState.hasConsent).toBe(false);

      // Grant consent
      await service.grantConsent();

      // Verify consent is granted
      expect(await service.shouldShowBanner()).toBe(false);
      expect(await service.hasValidConsent()).toBe(true);

      const consentState = await service.getConsentState();
      expect(consentState.hasConsent).toBe(true);
      expect(consentState.lastUpdated).toBeInstanceOf(Date);

      // Verify localStorage contains correct data
      const storedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
      expect(storedConsent).toBeTruthy();
      
      const parsedConsent = JSON.parse(storedConsent!);
      expect(parsedConsent.granted).toBe(true);
      expect(parsedConsent.version).toBe(COOKIE_CONSENT_VERSION);
    });

    it('should handle consent revocation flow', async () => {
      // Grant consent first
      await service.grantConsent();
      expect(await service.hasValidConsent()).toBe(true);

      // Revoke consent
      await service.revokeConsent();

      // Verify consent is revoked
      expect(await service.shouldShowBanner()).toBe(true);
      expect(await service.hasValidConsent()).toBe(false);

      const consentState = await service.getConsentState();
      expect(consentState.hasConsent).toBe(false);

      // Verify localStorage is cleared
      expect(localStorage.getItem(COOKIE_CONSENT_KEY)).toBeNull();
      expect(localStorage.getItem(COOKIE_PREFERENCES_KEY)).toBeNull();
    });

    it('should handle preferences update flow', async () => {
      // Grant consent with default preferences
      await service.grantConsent();

      const initialPreferences = await repository.getPreferences();
      expect(initialPreferences.functional).toBe(true);

      // Update preferences
      const newPreferences = {
        functional: true,
        analytics: true,
        marketing: false,
      };

      await service.updatePreferences(newPreferences);

      // Verify preferences are updated
      const updatedPreferences = await repository.getPreferences();
      expect(updatedPreferences).toEqual(newPreferences);

      // Verify localStorage contains updated preferences
      const storedPreferences = localStorage.getItem(COOKIE_PREFERENCES_KEY);
      expect(storedPreferences).toBeTruthy();
      
      const parsedPreferences = JSON.parse(storedPreferences!);
      expect(parsedPreferences).toEqual(newPreferences);
    });

    it('should persist consent across service instances', async () => {
      // Grant consent with first service instance
      await service.grantConsent();

      // Create new service instance
      const newRepository = new LocalStorageCookieRepository();
      const newService = new CookieConsentService(newRepository);

      // Verify consent persists
      expect(await newService.hasValidConsent()).toBe(true);
      expect(await newService.shouldShowBanner()).toBe(false);

      const consentState = await newService.getConsentState();
      expect(consentState.hasConsent).toBe(true);
    });

    it('should handle corrupted localStorage data gracefully', async () => {
      // Set corrupted data in localStorage
      localStorage.setItem(COOKIE_CONSENT_KEY, 'invalid-json');
      localStorage.setItem(COOKIE_PREFERENCES_KEY, 'invalid-json');

      // Service should handle gracefully
      expect(await service.hasValidConsent()).toBe(false);
      expect(await service.shouldShowBanner()).toBe(true);

      const consentState = await service.getConsentState();
      expect(consentState.hasConsent).toBe(false);

      // Should be able to grant consent normally
      await service.grantConsent();
      expect(await service.hasValidConsent()).toBe(true);
    });
  });

  describe('Edge cases', () => {
    it('should handle missing localStorage gracefully', async () => {
      // Mock localStorage as undefined (SSR scenario)
      const originalLocalStorage = window.localStorage;
      // @ts-ignore
      delete window.localStorage;

      const ssrRepository = new LocalStorageCookieRepository();
      const ssrService = new CookieConsentService(ssrRepository);

      // Should not throw errors
      expect(await ssrService.hasValidConsent()).toBe(false);
      expect(await ssrService.shouldShowBanner()).toBe(true);

      // Restore localStorage
      window.localStorage = originalLocalStorage;
    });
  });
});
