# Script para crear ZIP de OposIA con archivos esenciales

Write-Host "🚀 Creando ZIP de OposIA..." -ForegroundColor Green

# Crear carpeta temporal
$tempDir = "OposIA_Release"
if (Test-Path $tempDir) { 
    Remove-Item $tempDir -Recurse -Force 
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

Write-Host "📁 Copiando archivos esenciales..." -ForegroundColor Yellow

# Copiar archivos de configuración esenciales
Copy-Item "package.json" $tempDir
Copy-Item "next.config.js" $tempDir
Copy-Item "tailwind.config.js" $tempDir
Copy-Item "tsconfig.json" $tempDir
Copy-Item ".eslintrc.json" $tempDir

# Copiar vercel.json si existe
if (Test-Path "vercel.json") {
    Copy-Item "vercel.json" $tempDir
}

# Copiar carpetas esenciales
Write-Host "📂 Copiando código fuente..." -ForegroundColor Yellow
Copy-Item "src" $tempDir -Recurse

Write-Host "📂 Copiando archivos públicos..." -ForegroundColor Yellow
if (Test-Path "public") {
    Copy-Item "public" $tempDir -Recurse
}

# Copiar archivos de inicio
Write-Host "🔧 Copiando archivos de inicio..." -ForegroundColor Yellow
Copy-Item "INICIAR_APLICACION.bat" $tempDir
Copy-Item "COMO_INICIAR.txt" $tempDir

# Crear archivo .env.local con configuración de ejemplo
Write-Host "⚙️ Creando archivo de configuración..." -ForegroundColor Yellow
$envContent = @"
# Configuración de OposIA - CONFIGURAR ANTES DE USAR

# Supabase Configuration (REQUERIDO)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# OpenAI Configuration (REQUERIDO)
OPENAI_API_KEY=your_openai_api_key_here

# Stripe Configuration (REQUERIDO para pagos)
STRIPE_SECRET_KEY=your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Admin Configuration
ADMIN_EMAILS=<EMAIL>

# Email Configuration (opcional)
RESEND_API_KEY=your_resend_api_key_here
"@

$envContent | Out-File -FilePath "$tempDir\.env.local" -Encoding UTF8

# Crear archivo de instrucciones simples
$instruccionesContent = @"
═══════════════════════════════════════════════════════════════════════════════
                                   OposIA
                        Sistema de Preparación de Oposiciones
                              con Inteligencia Artificial
═══════════════════════════════════════════════════════════════════════════════

🚀 INSTRUCCIONES DE INICIO

1. CONFIGURAR VARIABLES DE ENTORNO:
   - Abrir archivo .env.local
   - Reemplazar los valores de ejemplo con las claves reales
   - Guardar el archivo

2. INICIAR LA APLICACIÓN:
   - Hacer doble clic en: INICIAR_APLICACION.bat
   - Seguir las instrucciones en pantalla
   - La aplicación se abrirá en: http://localhost:3000

📋 REQUISITOS:
   ✅ Node.js 18+ (https://nodejs.org/)
   ✅ Conexión a internet
   ✅ Cuentas configuradas en:
      - Supabase (base de datos)
      - OpenAI (inteligencia artificial)
      - Stripe (pagos)

🔧 SOLUCIÓN DE PROBLEMAS:
   - Si Node.js no está instalado: descargar desde nodejs.org
   - Si hay errores: verificar las variables de entorno en .env.local
   - Si el puerto 3000 está ocupado: la aplicación usará otro puerto automáticamente

🌟 FUNCIONALIDADES PRINCIPALES:
   📄 Gestión de documentos PDF
   📝 Generación de tests con IA
   🃏 Creación de flashcards
   🧠 Mapas mentales automáticos
   💬 Chat tutor inteligente
   📅 Planificación de estudios
   👤 Sistema de usuarios
   💳 Gestión de suscripciones

═══════════════════════════════════════════════════════════════════════════════
"@

$instruccionesContent | Out-File -FilePath "$tempDir\INSTRUCCIONES.txt" -Encoding UTF8

# Crear archivo README básico
$readmeContent = @"
# OposIA - Sistema de Preparacion de Oposiciones

## Inicio Rapido

1. Configurar variables de entorno: Editar .env.local con las claves reales
2. Iniciar aplicacion: Ejecutar INICIAR_APLICACION.bat
3. Abrir navegador: http://localhost:3000

## Requisitos

- Node.js 18+
- Cuentas en Supabase, OpenAI y Stripe

## Estructura

- src/ - Codigo fuente
- public/ - Archivos estaticos
- .env.local - Variables de entorno
- INICIAR_APLICACION.bat - Iniciador automatico

Para mas informacion, ver INSTRUCCIONES.txt
"@

$readmeContent | Out-File -FilePath "$tempDir\README.md" -Encoding UTF8

# Crear el ZIP
Write-Host "📦 Creando archivo ZIP..." -ForegroundColor Green
$zipPath = "OposIA_Release.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -CompressionLevel Optimal

# Limpiar carpeta temporal
Remove-Item $tempDir -Recurse -Force

Write-Host "✅ ZIP creado exitosamente: $zipPath" -ForegroundColor Green
Write-Host "📊 Contenido incluido:" -ForegroundColor Cyan
Write-Host "   ✅ Código fuente completo (src/)" -ForegroundColor White
Write-Host "   ✅ Archivos de configuración" -ForegroundColor White
Write-Host "   ✅ Iniciador automático (.bat)" -ForegroundColor White
Write-Host "   ✅ Variables de entorno (.env.local)" -ForegroundColor White
Write-Host "   ✅ Instrucciones de uso" -ForegroundColor White
Write-Host "   ✅ Archivos públicos" -ForegroundColor White

$zipSize = (Get-Item $zipPath).Length / 1MB
Write-Host "📏 Tamaño del ZIP: $([math]::Round($zipSize, 2)) MB" -ForegroundColor Yellow

Write-Host "`n🎯 El ZIP está listo para envío de registro de propiedad intelectual" -ForegroundColor Green
