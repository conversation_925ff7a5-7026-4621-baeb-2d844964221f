// src/features/privacy/components/CookieConsentManager.tsx
// Componente para gestión de lógica de consentimiento de cookies

'use client';

import React from 'react';
import { useCookieConsent } from '../hooks/useCookieConsent';

interface CookieConsentManagerProps {
  children: (props: {
    showBanner: boolean;
    hasConsent: boolean;
    isLoading: boolean;
    grantConsent: () => Promise<void>;
    revokeConsent: () => Promise<void>;
    hideBanner: () => void;
  }) => React.ReactNode;
}

export default function CookieConsentManager({ children }: CookieConsentManagerProps) {
  const {
    consentState,
    isLoading,
    showBanner,
    grantConsent,
    revokeConsent,
    hideBanner,
  } = useCookieConsent();

  return (
    <>
      {children({
        showBanner,
        hasConsent: consentState.hasConsent,
        isLoading,
        grantConsent: () => grantConsent(),
        revokeConsent,
        hideBanner,
      })}
    </>
  );
}
